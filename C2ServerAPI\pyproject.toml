[tool.poetry]
name = "c2serverapi"
version = "1.3.1"
description = "A client for registering chivalry 2 servers to a server browser backend."
authors = [
    "<PERSON><PERSON> <<EMAIL>>", 
    "<PERSON> <<EMAIL>>"
]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.8,<3.13"
PyQt5 = "*"
pyperclip = "*"
pywin32 = "*"
"discord.py" = "*"

[tool.poetry.group.dev.dependencies]
pyinstaller = "^5.13.0"
pyinstaller-versionfile = "^2.1.1"
pip-licenses = "^4.3.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
